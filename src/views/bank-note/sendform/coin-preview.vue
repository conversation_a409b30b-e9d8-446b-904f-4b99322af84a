<template>
  <div class="coin-preview-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">{{ coinData?.display?.coinName || '钱币详情' }}</h1>
        <div class="header-code" v-if="coinData?.coin?.diyCode">
          编码：{{ coinData.coin.diyCode }}
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>

    <!-- 错误信息 -->
    <el-alert
      v-else-if="error"
      :title="error"
      type="error"
      show-icon
      :closable="false"
      class="error-alert"
    />

    <!-- 钱币详情主体 -->
    <div v-else-if="coinData" class="coin-details-main">
      <!-- 钱币图片轮播区域 -->
      <div class="image-carousel-section">
        <div v-if="coinImages.length > 0" class="carousel-container">
          <el-carousel
            :interval="3000"
            height="350px"
            indicator-position="outside"
            arrow="always"
            :autoplay="true"
            :loop="true"
            motion-blur
          >
            <el-carousel-item v-for="(image, index) in coinImages" :key="index">
              <div class="carousel-item-content">
                <div class="image-label">{{ getImageLabel(index) }}</div>
                <el-image
                  :src="image"
                  :preview-src-list="coinImages"
                  :initial-index="index"
                  fit="cover"
                  class="carousel-image"
                  :preview-teleported="true"
                />
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
        <div v-else class="no-image">
          <el-icon class="no-image-icon"><Picture /></el-icon>
          <p>暂无图片</p>
        </div>
      </div>

      <!-- 基本信息区域 -->
      <div class="basic-info-section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-row">
            <span class="info-label">钱币名称</span>
            <span class="info-value">{{ coinData?.display?.coinName || '未设置' }}</span>
          </div>

          <div class="info-row">
            <span class="info-label">版别</span>
            <span class="info-value">{{ coinData?.display?.serialNumberWithVersion || '未设置' }}</span>
          </div>

          <div class="info-row">
            <span class="info-label">编码</span>
            <span class="info-value code">{{ coinData?.coin?.diyCode || '未设置' }}</span>
          </div>

          <div class="info-row">
            <span class="info-label">发行银行</span>
            <span class="info-value">{{ coinData?.coin?.issueBank || '中国人民银行' }}</span>
          </div>

          <div class="info-row">
            <span class="info-label">冠号</span>
            <span class="info-value">{{ coinData?.coin?.serialNumber || '未设置' }}</span>
          </div>

          <div class="info-row">
            <span class="info-label">年代</span>
            <span class="info-value">{{ coinData?.coin?.yearInfo || '未设置' }}</span>
          </div>

          <div class="info-row">
            <span class="info-label">评级结果</span>
            <span class="info-value grade">{{ formatGradeScore(coinData?.coin?.gradeScore, coinData?.coin?.gradeScoreValue) }}</span>
          </div>

          <div class="info-row" v-if="coinData?.coin?.compensationLevel">
            <span class="info-label">赔付等级</span>
            <span class="info-value">{{ coinData.coin.compensationLevel }}</span>
          </div>

          <div class="info-row full-width" v-if="coinData?.coin?.remark">
            <span class="info-label">备注</span>
            <span class="info-value">{{ coinData.coin.remark }}</span>
          </div>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <div class="action-bar">
        <el-button @click="goBack" class="action-btn">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <el-button type="primary" @click="refresh" class="action-btn">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ElMessage } from 'element-plus';
  import axios from 'axios';
  import { API_BASE_URL } from '@/config/setting';
  import {
    Picture,
    ArrowLeft,
    Refresh
  } from '@element-plus/icons-vue';

  const route = useRoute();
  const router = useRouter();

  // 响应式数据
  const loading = ref(false);
  const error = ref('');
  const coinData = ref(null);

  // 计算属性
  const coinImages = computed(() => {
    const images = [];
    const coin = coinData.value?.coin;

    if (coin?.frontImage) {
      images.push(coin.frontImage);
    }
    if (coin?.backImage) {
      images.push(coin.backImage);
    }

    return images;
  });

  // 方法
  const loadCoinData = async () => {
    const diyCode = route.params.diyCode;
    if (!diyCode) {
      error.value = '缺少送评条码参数';
      return;
    }

    loading.value = true;
    error.value = '';

    try {
      // 直接调用公开接口，无需 token，使用原生 axios
      const res = await axios.get(
        `${API_BASE_URL}/pjosendform/preview/${diyCode}`
      );
      if (res.data.code === 0) {
        coinData.value = res.data.data;
      } else {
        throw new Error(res.data.message);
      }
    } catch (err) {
      error.value = err.message || '加载钱币详情失败';
      ElMessage.error(error.value);
    } finally {
      loading.value = false;
    }
  };

  // 格式化品相分数显示
  const formatGradeScore = (gradeScore, gradeScoreValue) => {
    if (!gradeScore && !gradeScoreValue) {
      return '未评级';
    }

    // 如果有 gradeScoreValue，从 gradeScore 中移除数字部分，然后组合显示
    if (gradeScoreValue && gradeScore) {
      // 移除 gradeScore 中的数字部分，得到文字部分
      const gradeText = gradeScore.replace(gradeScoreValue, '').trim();
      return `${gradeText} ${gradeScoreValue}`;
    }

    // 如果只有 gradeScore，直接显示
    if (gradeScore) {
      return gradeScore;
    }

    // 如果只有 gradeScoreValue，只显示数字
    if (gradeScoreValue) {
      return gradeScoreValue;
    }

    return '未评级';
  };

  // 获取图片标签
  const getImageLabel = (index) => {
    const labels = ['钱币正面', '钱币反面'];
    return labels[index] || `图片${index + 1}`;
  };

  const goBack = () => {
    router.back();
  };

  const refresh = () => {
    loadCoinData();
  };

  // 生命周期
  onMounted(() => {
    loadCoinData();
  });
</script>

<style scoped>
/* 全局容器 */
.coin-preview-container {
  min-height: 100vh;
  background: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 页面头部 */
.page-header {
  background: #fff;
  border-bottom: 1px solid #e9ecef;
  padding: 20px 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #212529;
  margin: 0 0 8px 0;
}

.header-code {
  font-size: 14px;
  color: #6c757d;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 加载状态 */
.loading-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

/* 错误信息 */
.error-alert {
  max-width: 1200px;
  margin: 20px auto;
}

/* 主要内容区域 */
.coin-details-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  padding-bottom: 100px;
}

/* 图片轮播区域 */
.image-carousel-section {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.carousel-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.carousel-item-content {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
}

.image-label {
  position: absolute;
  top: 12px;
  left: 12px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 12px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 4px;
  z-index: 2;
}

.carousel-image {
  width: 100%;
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  object-fit: cover;
}

.carousel-image:hover {
  transform: scale(1.02);
}

/* 轮播组件样式优化 */
.carousel-container :deep(.el-carousel__container) {
  border-radius: 8px;
  overflow: hidden;
}

.carousel-container :deep(.el-carousel__item) {
  border-radius: 8px;
  overflow: hidden;
}

/* 强制图片铺满容器 */
.carousel-container :deep(.el-image) {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
}

.carousel-container :deep(.el-image img) {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  border-radius: 8px;
}

.carousel-container :deep(.el-carousel__arrow) {
  background-color: rgba(0, 0, 0, 0.6);
  border: none;
  width: 40px;
  height: 40px;
}

.carousel-container :deep(.el-carousel__arrow:hover) {
  background-color: rgba(0, 0, 0, 0.8);
}

.carousel-container :deep(.el-carousel__indicators) {
  margin-top: 16px;
}

.carousel-container :deep(.el-carousel__indicator) {
  padding: 8px 4px;
}

.carousel-container :deep(.el-carousel__button) {
  width: 12px;
  height: 4px;
  border-radius: 2px;
  background-color: #c0c4cc;
}

.carousel-container :deep(.el-carousel__indicator.is-active .el-carousel__button) {
  background-color: #007bff;
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #6c757d;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
}

.no-image-icon {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.5;
}

.no-image p {
  margin: 0;
  font-size: 16px;
}

/* 基本信息区域 */
.basic-info-section {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #212529;
  margin: 0 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 2px solid #007bff;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 0;
}

.info-row {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s ease;
}

.info-row:hover {
  background-color: #f8f9fa;
  padding-left: 8px;
  padding-right: 8px;
  border-radius: 4px;
}

.info-row:last-child {
  border-bottom: none;
}

.info-row.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.info-label {
  font-size: 14px;
  font-weight: 600;
  color: #6c757d;
  min-width: 100px;
  margin-right: 16px;
}

.info-row.full-width .info-label {
  margin-right: 0;
  margin-bottom: 4px;
}

.info-value {
  font-size: 15px;
  color: #212529;
  flex: 1;
  word-break: break-word;
}

.info-value.code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #e9ecef;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
  display: inline-block;
}

.info-value.grade {
  font-weight: 600;
  color: #28a745;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1px solid #e9ecef;
  padding: 16px 20px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  justify-content: center;
  gap: 12px;
}

.action-btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .coin-details-main {
    padding: 16px;
    padding-bottom: 100px;
  }

  .image-carousel-section,
  .basic-info-section {
    padding: 20px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .info-label {
    min-width: auto;
    margin-right: 0;
    margin-bottom: 4px;
  }

  .action-bar {
    padding: 12px 16px;
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 20px;
  }

  .image-carousel-section {
    padding: 16px;
  }

  .carousel-container {
    max-width: 100%;
  }

  .carousel-container :deep(.el-carousel) {
    height: 280px !important;
  }

  .carousel-container :deep(.el-carousel__arrow) {
    width: 32px;
    height: 32px;
  }

  .image-label {
    font-size: 12px;
    padding: 4px 8px;
    top: 8px;
    left: 8px;
  }

  .basic-info-section {
    padding: 16px;
  }

  .section-title {
    font-size: 16px;
  }

  .info-label {
    font-size: 13px;
  }

  .info-value {
    font-size: 14px;
  }
}
</style>
