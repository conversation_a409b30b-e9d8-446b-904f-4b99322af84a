<template>
  <div class="coin-preview-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">{{ coinData?.display?.coinName || '钱币详情' }}</h1>
          <div class="header-meta">
            <span class="meta-item">
              <el-icon><Calendar /></el-icon>
              {{ coinData?.coin?.yearInfo || '未设置' }}
            </span>
            <span class="meta-item">
              <el-icon><OfficeBuilding /></el-icon>
              {{ coinData?.coin?.issueBank || '中国人民银行' }}
            </span>
          </div>
        </div>
        <div class="header-right">
          <div class="header-score">
            <div class="score-circle">
              <div class="score-value">
                {{ getScoreDisplay() }}
              </div>
              <div class="score-label">评级分数</div>
            </div>
          </div>
          <el-tag
            v-if="coinData?.coin?.diyCode"
            class="diy-code-tag"
            size="large"
          >
            {{ coinData.coin.diyCode }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 错误信息 -->
    <el-alert
      v-else-if="error"
      :title="error"
      type="error"
      show-icon
      :closable="false"
      class="error-alert"
    />

    <!-- 钱币详情主体 -->
    <div v-else-if="coinData" class="coin-details-main">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧：钱币图片展示 -->
        <div class="image-section">
          <div class="section-header">
            <h3>钱币图片</h3>
            <div class="section-actions">
              <el-button size="small" text @click="toggleImageView">
                <el-icon><View /></el-icon>
                {{ imageViewMode === 'grid' ? '列表视图' : '网格视图' }}
              </el-button>
            </div>
          </div>
          <div class="image-container">
            <div v-if="coinImages.length > 0" class="coin-images">
              <div class="image-gallery" :class="imageViewMode">
                <div
                  v-for="(image, index) in coinImages"
                  :key="index"
                  class="image-item"
                >
                  <el-image
                    :src="image"
                    :preview-src-list="coinImages"
                    :initial-index="index"
                    fit="cover"
                    class="coin-image"
                    :preview-teleported="true"
                  />
                  <div class="image-label">{{ getImageLabel(index) }}</div>
                </div>
              </div>
            </div>
            <div v-else class="no-image">
              <el-icon class="no-image-icon"><Picture /></el-icon>
              <p>暂无图片</p>
            </div>
          </div>
        </div>

        <!-- 右侧：钱币信息 -->
        <div class="info-section">
          <!-- 基本信息卡片 -->
          <div class="info-card basic-info">
            <div class="card-header">
              <h3>基本信息</h3>
              <el-tag :type="getAuthenticityType()" size="small">
                {{ coinData.coin?.authenticity || '未鉴定' }}
              </el-tag>
            </div>
            <div class="card-content">
              <div class="info-grid">
                <div class="info-row">
                  <span class="label">版别</span>
                  <span class="value">{{ coinData.display?.serialNumberWithVersion || '未设置' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">冠号</span>
                  <span class="value">{{ coinData.coin?.serialNumber || '未设置' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">年代</span>
                  <span class="value">{{ coinData.coin?.yearInfo || '未设置' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">发行银行</span>
                  <span class="value">{{ coinData.coin?.issueBank || '中国人民银行' }}</span>
                </div>
                <div v-if="coinData.coin?.remark" class="info-row full-width">
                  <span class="label">备注</span>
                  <span class="value">{{ coinData.coin.remark }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 评级信息卡片 -->
          <div class="info-card grade-info">
            <div class="card-header">
              <h3>评级信息</h3>
              <div class="grade-badge">
                <span class="grade-text">{{ formatGradeScore(coinData.coin?.gradeScore, coinData.coin?.gradeScoreValue) }}</span>
              </div>
            </div>
            <div class="card-content">
              <div class="info-grid">
                <div v-if="coinData.coin?.specialMark" class="info-row">
                  <span class="label">特殊标记</span>
                  <span class="value special-mark">{{ coinData.coin.specialMark }}</span>
                </div>
                <div v-if="coinData.coin?.compensationLevel" class="info-row">
                  <span class="label">赔付等级</span>
                  <span class="value">{{ coinData.coin.compensationLevel }}</span>
                </div>
                <div v-if="coinData.coin?.scoreRemarks" class="info-row full-width">
                  <span class="label">评分备注</span>
                  <span class="value">{{ coinData.coin.scoreRemarks }}</span>
                </div>
                <div v-if="coinData.coin?.inspectionNote" class="info-row full-width">
                  <span class="label">验货标注</span>
                  <span class="value">{{ coinData.coin.inspectionNote }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 送评信息卡片 -->
          <div class="info-card send-info">
            <div class="card-header">
              <h3>送评信息</h3>
              <el-icon class="header-icon"><Document /></el-icon>
            </div>
            <div class="card-content">
              <div class="info-grid">
                <div class="info-row">
                  <span class="label">送评单号</span>
                  <span class="value code">{{ coinData.coin?.sendnum || '未设置' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">客户姓名</span>
                  <span class="value">{{ coinData.display?.customerName || '未设置' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">送评公司</span>
                  <span class="value">{{ coinData.display?.companyName || '未设置' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">提交时间</span>
                  <span class="value">{{ formatDate(coinData.display?.submitDate) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 市场参考卡片 -->
          <div class="info-card market-info">
            <div class="card-header">
              <h3>市场参考</h3>
              <el-icon class="header-icon"><TrendCharts /></el-icon>
            </div>
            <div class="card-content">
              <div class="market-stats">
                <div class="stat-item">
                  <div class="stat-label">历史成交</div>
                  <div class="stat-value">--</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">市场估价</div>
                  <div class="stat-value">--</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">收藏热度</div>
                  <div class="stat-value">--</div>
                </div>
              </div>
              <div class="market-note">
                <el-icon><InfoFilled /></el-icon>
                <span>市场数据仅供参考，实际价值以专业评估为准</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <div class="action-bar">
        <div class="action-content">
          <div class="action-left">
            <div class="company-info">
              <div class="company-name">广东宝元鑫泉艺术品评估鉴定有限公司</div>
              <div class="company-desc">专业钱币评级认证服务 · 权威可信</div>
            </div>
          </div>
          <div class="action-right">
            <el-button @click="goBack" class="action-btn">
              <el-icon><ArrowLeft /></el-icon>
              返回
            </el-button>
            <el-button type="primary" @click="refresh" class="action-btn">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="success" @click="testPublicApi" class="action-btn">
              <el-icon><Connection /></el-icon>
              测试接口
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ElMessage } from 'element-plus';
  import { getCoinPreviewByDiyCode } from './api';
  import axios from 'axios';
  import { API_BASE_URL } from '@/config/setting';
  import {
    Picture,
    ArrowLeft,
    Refresh,
    Connection,
    Calendar,
    OfficeBuilding,
    View,
    Document,
    TrendCharts,
    InfoFilled
  } from '@element-plus/icons-vue';

  const route = useRoute();
  const router = useRouter();

  // 响应式数据
  const loading = ref(false);
  const error = ref('');
  const coinData = ref(null);
  const imageViewMode = ref('grid'); // 'grid' 或 'list'

  // 计算属性
  const coinImages = computed(() => {
    const images = [];
    const coin = coinData.value?.coin;

    if (coin?.frontImage) {
      images.push(coin.frontImage);
    }
    if (coin?.backImage) {
      images.push(coin.backImage);
    }

    return images;
  });

  // 是否有评级详情
  const hasGradeDetails = computed(() => {
    return (
      coinData.value?.coin?.scoreRemarks || coinData.value?.coin?.inspectionNote
    );
  });

  // 方法
  const loadCoinData = async () => {
    const diyCode = route.params.diyCode;
    if (!diyCode) {
      error.value = '缺少送评条码参数';
      return;
    }

    loading.value = true;
    error.value = '';

    try {
      // 直接调用公开接口，无需 token，使用原生 axios
      const res = await axios.get(
        `${API_BASE_URL}/pjosendform/preview/${diyCode}`
      );
      if (res.data.code === 0) {
        coinData.value = res.data.data;
      } else {
        throw new Error(res.data.message);
      }
    } catch (err) {
      error.value = err.message || '加载钱币详情失败';
      ElMessage.error(error.value);
    } finally {
      loading.value = false;
    }
  };

  const formatDate = (date) => {
    if (!date) return '未设置';
    return new Date(date).toLocaleString('zh-CN');
  };

  // 格式化品相分数显示
  const formatGradeScore = (gradeScore, gradeScoreValue) => {
    if (!gradeScore && !gradeScoreValue) {
      return '未评级';
    }

    // 如果有 gradeScoreValue，从 gradeScore 中移除数字部分，然后组合显示
    if (gradeScoreValue && gradeScore) {
      // 移除 gradeScore 中的数字部分，得到文字部分
      const gradeText = gradeScore.replace(gradeScoreValue, '').trim();
      return `${gradeText} ${gradeScoreValue}`;
    }

    // 如果只有 gradeScore，直接显示
    if (gradeScore) {
      return gradeScore;
    }

    // 如果只有 gradeScoreValue，只显示数字
    if (gradeScoreValue) {
      return gradeScoreValue;
    }

    return '未评级';
  };

  // 获取图片标签
  const getImageLabel = (index) => {
    const labels = ['正面', '背面', '侧面', '细节'];
    return labels[index] || `图片${index + 1}`;
  };

  // 获取分数显示
  const getScoreDisplay = () => {
    const score = formatGradeScore(coinData.value?.coin?.gradeScore, coinData.value?.coin?.gradeScoreValue);
    if (score === '未评级') return '--';
    // 提取数字部分
    const match = score.match(/(\d+)/);
    return match ? match[1] : '--';
  };

  // 获取真伪性标签类型
  const getAuthenticityType = () => {
    const authenticity = coinData.value?.coin?.authenticity;
    if (!authenticity || authenticity === '未鉴定') return 'info';
    if (authenticity.includes('真') || authenticity.includes('正品')) return 'success';
    if (authenticity.includes('假') || authenticity.includes('伪')) return 'danger';
    return 'warning';
  };

  // 切换图片视图模式
  const toggleImageView = () => {
    imageViewMode.value = imageViewMode.value === 'grid' ? 'list' : 'grid';
  };

  const goBack = () => {
    router.back();
  };

  const refresh = () => {
    loadCoinData();
  };

  // 测试公开接口
  const testPublicApi = async () => {
    const diyCode = route.params.diyCode;
    if (!diyCode) {
      ElMessage.warning('缺少送评条码参数');
      return;
    }

    try {
      // 直接调用公开接口测试
      const response = await fetch(
        `${API_BASE_URL}/pjosendform/preview/${diyCode}`
      );
      const data = await response.json();

      if (data.code === 0) {
        ElMessage.success('公开接口调用成功！');
        console.log('接口返回数据:', data.data);
      } else {
        ElMessage.error('接口调用失败: ' + data.message);
      }
    } catch (error) {
      ElMessage.error('接口调用出错: ' + error.message);
      console.error('接口调用错误:', error);
    }
  };

  // 生命周期
  onMounted(() => {
    loadCoinData();
  });
</script>

<style scoped>
/* 全局容器 */
.coin-preview-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
  margin: 0;
  font-family: 'PingFang SC', 'Microsoft Yahei', 'Helvetica Neue', Arial, sans-serif;
}

/* 页面头部 - 现代简约风格 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 0;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%);
  background-size: 20px 20px;
  opacity: 0.3;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

.header-meta {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  opacity: 0.9;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-score {
  display: flex;
  align-items: center;
}

.score-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.score-value {
  font-size: 24px;
  font-weight: 700;
  color: #2d3436;
  line-height: 1;
}

.score-label {
  font-size: 10px;
  color: #636e72;
  margin-top: 2px;
}

.diy-code-tag {
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid rgba(255, 255, 255, 0.5);
  color: #667eea;
  font-size: 14px;
  font-weight: 700;
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}

/* 加载状态 */
.loading-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 16px;
}

/* 错误信息 */
.error-alert {
  max-width: 1200px;
  margin: 20px auto;
}

/* 主要内容区域 */
.coin-details-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 16px 100px;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

/* 图片区域 */
.image-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.1);
  position: relative;
  overflow: hidden;
}

.image-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px 16px 0 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2d3436;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.image-container {
  width: 100%;
}

.image-gallery {
  display: grid;
  gap: 16px;
  transition: all 0.3s ease;
}

.image-gallery.grid {
  grid-template-columns: repeat(2, 1fr);
}

.image-gallery.list {
  grid-template-columns: 1fr;
}

.image-item {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(102, 126, 234, 0.1);
  background: white;
}

.image-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
}

.coin-image {
  width: 100%;
  height: 160px;
  border-radius: 12px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.image-gallery.list .coin-image {
  height: 200px;
}

.image-item:hover .coin-image {
  transform: scale(1.02);
}

.image-label {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(45, 52, 54, 0.8));
  color: white;
  padding: 12px 16px;
  font-size: 13px;
  font-weight: 600;
  text-align: center;
  backdrop-filter: blur(4px);
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #636e72;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  border: 2px dashed #ddd;
}

.no-image-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
  color: #667eea;
}

.no-image p {
  margin: 0;
  font-size: 16px;
}

/* 信息区域 */
.info-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 信息卡片 */
.info-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.1);
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);
}

.info-card.basic-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #74b9ff 0%, #0984e3 100%);
}

.info-card.grade-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ffeaa7 0%, #fdcb6e 100%);
}

.info-card.send-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #a29bfe 0%, #6c5ce7 100%);
}

.info-card.market-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #fd79a8 0%, #e84393 100%);
}

.card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 18px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2d3436;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  color: #667eea;
  font-size: 20px;
}

.grade-badge {
  background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
  color: #2d3436;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(253, 203, 110, 0.3);
}

.grade-text {
  font-family: 'DIN-Bold', monospace;
}

.card-content {
  padding: 24px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.info-row {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.info-row:hover {
  background: rgba(102, 126, 234, 0.02);
  border-radius: 8px;
  padding-left: 8px;
  padding-right: 8px;
}

.info-row:last-child {
  border-bottom: none;
}

.info-row.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
  align-items: stretch;
}

.info-row .label {
  font-weight: 600;
  color: #636e72;
  min-width: 80px;
  margin-right: 16px;
  font-size: 14px;
}

.info-row.full-width .label {
  margin-bottom: 8px;
  margin-right: 0;
}

.info-row .value {
  color: #2d3436;
  font-weight: 500;
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
}

.info-row .value.code {
  font-family: 'Courier New', monospace;
  background: rgba(102, 126, 234, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
}

.info-row .value.special-mark {
  background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
  color: #2d3436;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
}

/* 市场参考样式 */
.market-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-label {
  font-size: 12px;
  color: #636e72;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #2d3436;
  font-family: 'DIN-Bold', monospace;
}

.market-note {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: rgba(116, 185, 255, 0.1);
  border-radius: 8px;
  font-size: 13px;
  color: #636e72;
}

.market-note .el-icon {
  color: #74b9ff;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-top: 1px solid rgba(102, 126, 234, 0.2);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.action-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-left {
  flex: 1;
}

.company-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.company-name {
  color: #2d3436;
  font-size: 14px;
  font-weight: 600;
}

.company-desc {
  color: #636e72;
  font-size: 12px;
  font-weight: 400;
}

.action-right {
  display: flex;
  gap: 12px;
}

.action-btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
    padding: 0 16px;
  }

  .header-right {
    flex-direction: column;
    gap: 12px;
  }

  .page-title {
    font-size: 24px;
  }

  .header-meta {
    justify-content: center;
  }

  .image-gallery.grid {
    grid-template-columns: 1fr;
  }

  .coin-image {
    height: 200px;
  }

  .coin-details-main {
    padding: 20px 16px 120px;
  }

  .market-stats {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .action-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .action-right {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 20px;
  }

  .score-circle {
    width: 60px;
    height: 60px;
  }

  .score-value {
    font-size: 18px;
  }

  .score-label {
    font-size: 9px;
  }

  .coin-image {
    height: 180px;
  }

  .action-right {
    flex-direction: column;
    gap: 8px;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .info-row .label {
    min-width: auto;
    margin-right: 0;
    margin-bottom: 4px;
  }

  .market-stats {
    grid-template-columns: 1fr;
  }

  .stat-item {
    padding: 12px;
  }
}
</style>
